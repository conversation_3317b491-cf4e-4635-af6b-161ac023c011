{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,KAA4C;QAA5C,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB,GAA5C;IACb,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/lib/api.ts"], "sourcesContent": ["// API utility functions for the AI Tutor Platform\n\nimport { \n  User, \n  Subject, \n  StudyMaterial, \n  ChatMessage, \n  TeacherAvatar, \n  Announcement, \n  Analytics,\n  ApiResponse,\n  PaginatedResponse,\n  S3SignedUrl,\n  StreamingResponse\n} from '@/types';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';\n\n// Generic API request function\nasync function apiRequest<T>(\n  endpoint: string,\n  options: RequestInit = {}\n): Promise<ApiResponse<T>> {\n  try {\n    const response = await fetch(`${API_BASE_URL}${endpoint}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers,\n      },\n      ...options,\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('API request failed:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error occurred',\n    };\n  }\n}\n\n// Authentication APIs\nexport const authApi = {\n  // Get current user profile\n  getMe: (): Promise<ApiResponse<User>> => \n    apiRequest<User>('/me'),\n\n  // Update user profile\n  updateProfile: (data: Partial<User>): Promise<ApiResponse<User>> =>\n    apiRequest<User>('/me', {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n};\n\n// Subject APIs\nexport const subjectApi = {\n  // Get all subjects for current user\n  getSubjects: (): Promise<ApiResponse<Subject[]>> =>\n    apiRequest<Subject[]>('/subjects'),\n\n  // Get subject by ID\n  getSubject: (id: string): Promise<ApiResponse<Subject>> =>\n    apiRequest<Subject>(`/subjects/${id}`),\n\n  // Create new subject (teacher/admin only)\n  createSubject: (data: Omit<Subject, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Subject>> =>\n    apiRequest<Subject>('/subjects', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Update subject (teacher/admin only)\n  updateSubject: (id: string, data: Partial<Subject>): Promise<ApiResponse<Subject>> =>\n    apiRequest<Subject>(`/subjects/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n\n  // Delete subject (teacher/admin only)\n  deleteSubject: (id: string): Promise<ApiResponse<void>> =>\n    apiRequest<void>(`/subjects/${id}`, {\n      method: 'DELETE',\n    }),\n};\n\n// Study Material APIs\nexport const materialApi = {\n  // Get materials for a subject\n  getMaterials: (subjectId: string): Promise<ApiResponse<StudyMaterial[]>> =>\n    apiRequest<StudyMaterial[]>(`/materials?subjectId=${subjectId}`),\n\n  // Upload new material\n  uploadMaterial: (data: FormData): Promise<ApiResponse<StudyMaterial>> =>\n    fetch(`${API_BASE_URL}/materials`, {\n      method: 'POST',\n      body: data,\n    }).then(res => res.json()),\n\n  // Delete material\n  deleteMaterial: (id: string): Promise<ApiResponse<void>> =>\n    apiRequest<void>(`/materials/${id}`, {\n      method: 'DELETE',\n    }),\n};\n\n// Chat APIs\nexport const chatApi = {\n  // Send message and get streaming response\n  sendMessage: async function* (\n    message: string,\n    subjectId?: string\n  ): AsyncGenerator<StreamingResponse, void, unknown> {\n    const response = await fetch(`${API_BASE_URL}/chat`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ message, subjectId }),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    const reader = response.body?.getReader();\n    if (!reader) {\n      throw new Error('No response body');\n    }\n\n    const decoder = new TextDecoder();\n    let buffer = '';\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read();\n        if (done) break;\n\n        buffer += decoder.decode(value, { stream: true });\n        const lines = buffer.split('\\n');\n        buffer = lines.pop() || '';\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            const data = line.slice(6);\n            if (data === '[DONE]') {\n              return;\n            }\n            try {\n              const parsed: StreamingResponse = JSON.parse(data);\n              yield parsed;\n            } catch (e) {\n              console.error('Failed to parse SSE data:', e);\n            }\n          }\n        }\n      }\n    } finally {\n      reader.releaseLock();\n    }\n  },\n\n  // Get chat history\n  getChatHistory: (subjectId?: string): Promise<ApiResponse<ChatMessage[]>> =>\n    apiRequest<ChatMessage[]>(`/chat/history${subjectId ? `?subjectId=${subjectId}` : ''}`),\n};\n\n// Text-to-Speech APIs\nexport const ttsApi = {\n  // Get TTS audio and visemes\n  getTTS: (messageId: string): Promise<ApiResponse<{ audioUrl: string; visemes: any[] }>> =>\n    apiRequest<{ audioUrl: string; visemes: any[] }>(`/tts?id=${messageId}`),\n};\n\n// Avatar APIs\nexport const avatarApi = {\n  // Get teacher avatar configuration\n  getAvatar: (teacherId?: string): Promise<ApiResponse<TeacherAvatar>> =>\n    apiRequest<TeacherAvatar>(`/avatar${teacherId ? `?teacherId=${teacherId}` : ''}`),\n\n  // Save teacher avatar configuration\n  saveAvatar: (data: Omit<TeacherAvatar, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<TeacherAvatar>> =>\n    apiRequest<TeacherAvatar>('/avatar', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Update avatar configuration\n  updateAvatar: (id: string, data: Partial<TeacherAvatar>): Promise<ApiResponse<TeacherAvatar>> =>\n    apiRequest<TeacherAvatar>(`/avatar/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n};\n\n// S3 Upload APIs\nexport const s3Api = {\n  // Get signed URL for file upload\n  getSignedUrl: (fileName: string, fileType: string): Promise<ApiResponse<S3SignedUrl>> =>\n    apiRequest<S3SignedUrl>('/s3/sign', {\n      method: 'POST',\n      body: JSON.stringify({ fileName, fileType }),\n    }),\n\n  // Upload file to S3 using signed URL\n  uploadFile: async (file: File, signedUrl: S3SignedUrl): Promise<boolean> => {\n    try {\n      const formData = new FormData();\n      Object.entries(signedUrl.fields).forEach(([key, value]) => {\n        formData.append(key, value);\n      });\n      formData.append('file', file);\n\n      const response = await fetch(signedUrl.url, {\n        method: 'POST',\n        body: formData,\n      });\n\n      return response.ok;\n    } catch (error) {\n      console.error('S3 upload failed:', error);\n      return false;\n    }\n  },\n};\n\n// Announcement APIs\nexport const announcementApi = {\n  // Get announcements\n  getAnnouncements: (): Promise<ApiResponse<Announcement[]>> =>\n    apiRequest<Announcement[]>('/announcements'),\n\n  // Create announcement (HOD/Admin only)\n  createAnnouncement: (data: Omit<Announcement, 'id' | 'authorId' | 'authorName' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Announcement>> =>\n    apiRequest<Announcement>('/announcements', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Delete announcement\n  deleteAnnouncement: (id: string): Promise<ApiResponse<void>> =>\n    apiRequest<void>(`/announcements/${id}`, {\n      method: 'DELETE',\n    }),\n};\n\n// Analytics APIs\nexport const analyticsApi = {\n  // Get analytics data (HOD/Admin only)\n  getAnalytics: (): Promise<ApiResponse<Analytics>> =>\n    apiRequest<Analytics>('/analytics'),\n\n  // Get user analytics\n  getUserAnalytics: (userId?: string): Promise<ApiResponse<any>> =>\n    apiRequest<any>(`/analytics/users${userId ? `/${userId}` : ''}`),\n};\n\n// User Management APIs (Admin only)\nexport const userApi = {\n  // Get all users\n  getUsers: (page = 1, limit = 20): Promise<PaginatedResponse<User>> =>\n    apiRequest<User[]>(`/users?page=${page}&limit=${limit}`),\n\n  // Create user\n  createUser: (data: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<User>> =>\n    apiRequest<User>('/users', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Update user\n  updateUser: (id: string, data: Partial<User>): Promise<ApiResponse<User>> =>\n    apiRequest<User>(`/users/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n\n  // Delete user\n  deleteUser: (id: string): Promise<ApiResponse<void>> =>\n    apiRequest<void>(`/users/${id}`, {\n      method: 'DELETE',\n    }),\n};\n\n// Mock data for development (remove when backend is ready)\nexport const mockData = {\n  user: {\n    id: '1',\n    name: 'John Doe',\n    email: '<EMAIL>',\n    role: 'student' as const,\n    orgId: 'org1',\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  subjects: [\n    {\n      id: '1',\n      name: 'Japanese Language',\n      description: 'Learn Japanese with AI assistance',\n      teacherId: 'teacher1',\n      teacherName: 'Tanaka Sensei',\n      orgId: 'org1',\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    },\n    {\n      id: '2',\n      name: 'Mathematics',\n      description: 'Advanced mathematics concepts',\n      teacherId: 'teacher2',\n      teacherName: 'Smith Sensei',\n      orgId: 'org1',\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    },\n  ],\n  messages: [\n    {\n      id: '1',\n      content: 'Hello! How can I help you today?',\n      role: 'assistant' as const,\n      timestamp: new Date(),\n      subjectId: '1',\n    },\n    {\n      id: '2',\n      content: 'Can you explain the difference between は and が?',\n      role: 'user' as const,\n      timestamp: new Date(),\n      subjectId: '1',\n    },\n  ],\n};\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;;;;;;;;;;;AAgB7B;AAArB,MAAM,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAExD,+BAA+B;AAC/B,eAAe,WACb,QAAgB;QAChB,UAAA,iEAAuB,CAAC;IAExB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,AAAC,GAAiB,OAAf,cAAwB,OAAT,WAAY;YACzD,SAAS;gBACP,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACpB;YACA,GAAG,OAAO;QACZ;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,AAAC,uBAAsC,OAAhB,SAAS,MAAM;QACxD;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,MAAM,UAAU;IACrB,2BAA2B;IAC3B,OAAO,IACL,WAAiB;IAEnB,sBAAsB;IACtB,eAAe,CAAC,OACd,WAAiB,OAAO;YACtB,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;AACJ;AAGO,MAAM,aAAa;IACxB,oCAAoC;IACpC,aAAa,IACX,WAAsB;IAExB,oBAAoB;IACpB,YAAY,CAAC,KACX,WAAoB,AAAC,aAAe,OAAH;IAEnC,0CAA0C;IAC1C,eAAe,CAAC,OACd,WAAoB,aAAa;YAC/B,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,sCAAsC;IACtC,eAAe,CAAC,IAAY,OAC1B,WAAoB,AAAC,aAAe,OAAH,KAAM;YACrC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,sCAAsC;IACtC,eAAe,CAAC,KACd,WAAiB,AAAC,aAAe,OAAH,KAAM;YAClC,QAAQ;QACV;AACJ;AAGO,MAAM,cAAc;IACzB,8BAA8B;IAC9B,cAAc,CAAC,YACb,WAA4B,AAAC,wBAAiC,OAAV;IAEtD,sBAAsB;IACtB,gBAAgB,CAAC,OACf,MAAM,AAAC,GAAe,OAAb,cAAa,eAAa;YACjC,QAAQ;YACR,MAAM;QACR,GAAG,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI;IAEzB,kBAAkB;IAClB,gBAAgB,CAAC,KACf,WAAiB,AAAC,cAAgB,OAAH,KAAM;YACnC,QAAQ;QACV;AACJ;AAGO,MAAM,UAAU;IACrB,0CAA0C;IAC1C,aAAa,gBACX,OAAe,EACf,SAAkB;YAcH;QAZf,MAAM,WAAW,MAAM,MAAM,AAAC,GAAe,OAAb,cAAa,UAAQ;YACnD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAS;YAAU;QAC5C;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,AAAC,uBAAsC,OAAhB,SAAS,MAAM;QACxD;QAEA,MAAM,UAAS,iBAAA,SAAS,IAAI,cAAb,qCAAA,eAAe,SAAS;QACvC,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,UAAU,IAAI;QACpB,IAAI,SAAS;QAEb,IAAI;YACF,MAAO,KAAM;gBACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;gBACzC,IAAI,MAAM;gBAEV,UAAU,QAAQ,MAAM,CAAC,OAAO;oBAAE,QAAQ;gBAAK;gBAC/C,MAAM,QAAQ,OAAO,KAAK,CAAC;gBAC3B,SAAS,MAAM,GAAG,MAAM;gBAExB,KAAK,MAAM,QAAQ,MAAO;oBACxB,IAAI,KAAK,UAAU,CAAC,WAAW;wBAC7B,MAAM,OAAO,KAAK,KAAK,CAAC;wBACxB,IAAI,SAAS,UAAU;4BACrB;wBACF;wBACA,IAAI;4BACF,MAAM,SAA4B,KAAK,KAAK,CAAC;4BAC7C,MAAM;wBACR,EAAE,OAAO,GAAG;4BACV,QAAQ,KAAK,CAAC,6BAA6B;wBAC7C;oBACF;gBACF;YACF;QACF,SAAU;YACR,OAAO,WAAW;QACpB;IACF;IAEA,mBAAmB;IACnB,gBAAgB,CAAC,YACf,WAA0B,AAAC,gBAA0D,OAA3C,YAAY,AAAC,cAAuB,OAAV,aAAc;AACtF;AAGO,MAAM,SAAS;IACpB,4BAA4B;IAC5B,QAAQ,CAAC,YACP,WAAiD,AAAC,WAAoB,OAAV;AAChE;AAGO,MAAM,YAAY;IACvB,mCAAmC;IACnC,WAAW,CAAC,YACV,WAA0B,AAAC,UAAoD,OAA3C,YAAY,AAAC,cAAuB,OAAV,aAAc;IAE9E,oCAAoC;IACpC,YAAY,CAAC,OACX,WAA0B,WAAW;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,8BAA8B;IAC9B,cAAc,CAAC,IAAY,OACzB,WAA0B,AAAC,WAAa,OAAH,KAAM;YACzC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;AACJ;AAGO,MAAM,QAAQ;IACnB,iCAAiC;IACjC,cAAc,CAAC,UAAkB,WAC/B,WAAwB,YAAY;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAU;YAAS;QAC5C;IAEF,qCAAqC;IACrC,YAAY,OAAO,MAAY;QAC7B,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,OAAO,OAAO,CAAC,UAAU,MAAM,EAAE,OAAO,CAAC;oBAAC,CAAC,KAAK,MAAM;gBACpD,SAAS,MAAM,CAAC,KAAK;YACvB;YACA,SAAS,MAAM,CAAC,QAAQ;YAExB,MAAM,WAAW,MAAM,MAAM,UAAU,GAAG,EAAE;gBAC1C,QAAQ;gBACR,MAAM;YACR;YAEA,OAAO,SAAS,EAAE;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;QACT;IACF;AACF;AAGO,MAAM,kBAAkB;IAC7B,oBAAoB;IACpB,kBAAkB,IAChB,WAA2B;IAE7B,uCAAuC;IACvC,oBAAoB,CAAC,OACnB,WAAyB,kBAAkB;YACzC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,sBAAsB;IACtB,oBAAoB,CAAC,KACnB,WAAiB,AAAC,kBAAoB,OAAH,KAAM;YACvC,QAAQ;QACV;AACJ;AAGO,MAAM,eAAe;IAC1B,sCAAsC;IACtC,cAAc,IACZ,WAAsB;IAExB,qBAAqB;IACrB,kBAAkB,CAAC,SACjB,WAAgB,AAAC,mBAA6C,OAA3B,SAAS,AAAC,IAAU,OAAP,UAAW;AAC/D;AAGO,MAAM,UAAU;IACrB,gBAAgB;IAChB,UAAU;YAAC,wEAAO,GAAG,yEAAQ;eAC3B,WAAmB,AAAC,eAA4B,OAAd,MAAK,WAAe,OAAN;;IAElD,cAAc;IACd,YAAY,CAAC,OACX,WAAiB,UAAU;YACzB,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,cAAc;IACd,YAAY,CAAC,IAAY,OACvB,WAAiB,AAAC,UAAY,OAAH,KAAM;YAC/B,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,cAAc;IACd,YAAY,CAAC,KACX,WAAiB,AAAC,UAAY,OAAH,KAAM;YAC/B,QAAQ;QACV;AACJ;AAGO,MAAM,WAAW;IACtB,MAAM;QACJ,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,OAAO;QACP,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA,UAAU;QACR;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,WAAW;YACX,aAAa;YACb,OAAO;YACP,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,WAAW;YACX,aAAa;YACb,OAAO;YACP,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;KACD;IACD,UAAU;QACR;YACE,IAAI;YACJ,SAAS;YACT,MAAM;YACN,WAAW,IAAI;YACf,WAAW;QACb;QACA;YACE,IAAI;YACJ,SAAS;YACT,MAAM;YACN,WAAW,IAAI;YACf,WAAW;QACb;KACD;AACH", "debugId": null}}, {"offset": {"line": 457, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/store/useUser.ts"], "sourcesContent": ["// Zustand store for User and App state management\n\nimport { create } from 'zustand';\nimport { devtools, persist } from 'zustand/middleware';\nimport { User, Subject, StudyMaterial, Announcement, Analytics, UserStore } from '@/types';\nimport { authApi, subjectApi, materialApi, announcementApi, analyticsApi, mockData } from '@/lib/api';\n\nexport const useUser = create<UserStore>()(\n  devtools(\n    persist(\n      (set, get) => ({\n        // State\n        user: undefined,\n        subjects: [],\n        materials: [],\n        announcements: [],\n        analytics: undefined,\n\n        // Actions\n        setUser: (user: User | undefined) => {\n          set({ user }, false, 'setUser');\n        },\n\n        setSubjects: (subjects: Subject[]) => {\n          set({ subjects }, false, 'setSubjects');\n        },\n\n        setMaterials: (materials: StudyMaterial[]) => {\n          set({ materials }, false, 'setMaterials');\n        },\n\n        setAnnouncements: (announcements: Announcement[]) => {\n          set({ announcements }, false, 'setAnnouncements');\n        },\n\n        setAnalytics: (analytics: Analytics) => {\n          set({ analytics }, false, 'setAnalytics');\n        },\n      }),\n      {\n        name: 'user-store',\n        partialize: (state) => ({\n          user: state.user,\n          subjects: state.subjects,\n          announcements: state.announcements,\n        }),\n      }\n    ),\n    {\n      name: 'user-store',\n    }\n  )\n);\n\n// Selectors for optimized re-renders\nexport const useCurrentUser = () => useUser((state) => state.user);\nexport const useSubjects = () => useUser((state) => state.subjects);\nexport const useMaterials = () => useUser((state) => state.materials);\nexport const useAnnouncements = () => useUser((state) => state.announcements);\nexport const useAnalytics = () => useUser((state) => state.analytics);\n\n// Actions selectors\nexport const useUserActions = () => useUser((state) => ({\n  setUser: state.setUser,\n  setSubjects: state.setSubjects,\n  setMaterials: state.setMaterials,\n  setAnnouncements: state.setAnnouncements,\n  setAnalytics: state.setAnalytics,\n}));\n\n// Authentication helpers\nexport const useAuth = () => {\n  const { user, setUser } = useUser();\n\n  const login = async (email: string, password: string) => {\n    try {\n      // TODO: Implement actual login when NextAuth is configured\n      console.log('Logging in:', email);\n      \n      // For now, use mock data\n      setUser(mockData.user);\n      return { success: true };\n    } catch (error) {\n      console.error('Login failed:', error);\n      return { success: false, error: 'Login failed' };\n    }\n  };\n\n  const logout = async () => {\n    try {\n      // TODO: Implement actual logout when NextAuth is configured\n      setUser(undefined);\n      // Clear other stores if needed\n      return { success: true };\n    } catch (error) {\n      console.error('Logout failed:', error);\n      return { success: false, error: 'Logout failed' };\n    }\n  };\n\n  const loadUserProfile = async () => {\n    try {\n      // TODO: Implement actual API call when backend is ready\n      // const response = await authApi.getMe();\n      console.log('Loading user profile');\n      \n      // For now, use mock data\n      setUser(mockData.user);\n      return { success: true };\n    } catch (error) {\n      console.error('Failed to load user profile:', error);\n      return { success: false, error: 'Failed to load profile' };\n    }\n  };\n\n  return {\n    user,\n    login,\n    logout,\n    loadUserProfile,\n    isAuthenticated: !!user,\n    isStudent: user?.role === 'student',\n    isTeacher: user?.role === 'teacher',\n    isHOD: user?.role === 'hod',\n    isAdmin: user?.role === 'admin',\n  };\n};\n\n// Data loading helpers\nexport const useDataLoaders = () => {\n  const { setSubjects, setMaterials, setAnnouncements, setAnalytics } = useUserActions();\n  const { user } = useAuth();\n\n  const loadSubjects = async () => {\n    try {\n      // TODO: Implement actual API call when backend is ready\n      // const response = await subjectApi.getSubjects();\n      console.log('Loading subjects');\n      \n      // For now, use mock data\n      setSubjects(mockData.subjects);\n      return { success: true };\n    } catch (error) {\n      console.error('Failed to load subjects:', error);\n      return { success: false, error: 'Failed to load subjects' };\n    }\n  };\n\n  const loadMaterials = async (subjectId?: string) => {\n    try {\n      // TODO: Implement actual API call when backend is ready\n      // const response = await materialApi.getMaterials(subjectId);\n      console.log('Loading materials for subject:', subjectId);\n      \n      // For now, use empty array\n      setMaterials([]);\n      return { success: true };\n    } catch (error) {\n      console.error('Failed to load materials:', error);\n      return { success: false, error: 'Failed to load materials' };\n    }\n  };\n\n  const loadAnnouncements = async () => {\n    try {\n      // TODO: Implement actual API call when backend is ready\n      // const response = await announcementApi.getAnnouncements();\n      console.log('Loading announcements');\n      \n      // For now, use empty array\n      setAnnouncements([]);\n      return { success: true };\n    } catch (error) {\n      console.error('Failed to load announcements:', error);\n      return { success: false, error: 'Failed to load announcements' };\n    }\n  };\n\n  const loadAnalytics = async () => {\n    if (!user || (user.role !== 'hod' && user.role !== 'admin')) {\n      return { success: false, error: 'Unauthorized' };\n    }\n\n    try {\n      // TODO: Implement actual API call when backend is ready\n      // const response = await analyticsApi.getAnalytics();\n      console.log('Loading analytics');\n      \n      // For now, use mock analytics data\n      const mockAnalytics: Analytics = {\n        totalUsers: 150,\n        totalStudents: 120,\n        totalTeachers: 25,\n        totalSubjects: 15,\n        totalMaterials: 85,\n        totalChats: 1250,\n        activeUsersToday: 45,\n        activeUsersThisWeek: 98,\n        popularSubjects: [\n          { subjectId: '1', subjectName: 'Japanese Language', chatCount: 450 },\n          { subjectId: '2', subjectName: 'Mathematics', chatCount: 320 },\n        ],\n        userGrowth: [\n          { date: '2024-01-01', count: 100 },\n          { date: '2024-02-01', count: 120 },\n          { date: '2024-03-01', count: 150 },\n        ],\n      };\n      \n      setAnalytics(mockAnalytics);\n      return { success: true };\n    } catch (error) {\n      console.error('Failed to load analytics:', error);\n      return { success: false, error: 'Failed to load analytics' };\n    }\n  };\n\n  const loadAllData = async () => {\n    const results = await Promise.allSettled([\n      loadSubjects(),\n      loadAnnouncements(),\n      ...(user?.role === 'hod' || user?.role === 'admin' ? [loadAnalytics()] : []),\n    ]);\n\n    const failures = results.filter(result => result.status === 'rejected');\n    if (failures.length > 0) {\n      console.error('Some data failed to load:', failures);\n    }\n\n    return { success: failures.length === 0 };\n  };\n\n  return {\n    loadSubjects,\n    loadMaterials,\n    loadAnnouncements,\n    loadAnalytics,\n    loadAllData,\n  };\n};\n\n// Subject helpers\nexport const useSubjectHelpers = () => {\n  const subjects = useSubjects();\n  const { user } = useAuth();\n\n  const getSubjectById = (id: string) => {\n    return subjects.find(subject => subject.id === id);\n  };\n\n  const getSubjectsByTeacher = (teacherId: string) => {\n    return subjects.filter(subject => subject.teacherId === teacherId);\n  };\n\n  const getUserSubjects = () => {\n    if (!user) return [];\n    \n    if (user.role === 'student') {\n      // Students see all subjects in their org\n      return subjects.filter(subject => subject.orgId === user.orgId);\n    } else if (user.role === 'teacher') {\n      // Teachers see only their subjects\n      return subjects.filter(subject => subject.teacherId === user.id);\n    } else {\n      // HOD and Admin see all subjects in their org\n      return subjects.filter(subject => subject.orgId === user.orgId);\n    }\n  };\n\n  return {\n    getSubjectById,\n    getSubjectsByTeacher,\n    getUserSubjects,\n  };\n};\n\n// Role-based permissions\nexport const usePermissions = () => {\n  const { user } = useAuth();\n\n  const canCreateSubject = () => {\n    return user?.role === 'teacher' || user?.role === 'admin';\n  };\n\n  const canEditSubject = (subjectId: string) => {\n    if (!user) return false;\n    if (user.role === 'admin') return true;\n    \n    const subject = useUser.getState().subjects.find(s => s.id === subjectId);\n    return user.role === 'teacher' && subject?.teacherId === user.id;\n  };\n\n  const canUploadMaterial = (subjectId: string) => {\n    return canEditSubject(subjectId);\n  };\n\n  const canCreateAnnouncement = () => {\n    return user?.role === 'hod' || user?.role === 'admin';\n  };\n\n  const canViewAnalytics = () => {\n    return user?.role === 'hod' || user?.role === 'admin';\n  };\n\n  const canManageUsers = () => {\n    return user?.role === 'admin';\n  };\n\n  return {\n    canCreateSubject,\n    canEditSubject,\n    canUploadMaterial,\n    canCreateAnnouncement,\n    canViewAnalytics,\n    canManageUsers,\n  };\n};\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;;;;;;;;;;;AAElD;AACA;AAEA;;;;;AAEO,MAAM,UAAU,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC1B,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,QAAQ;QACR,MAAM;QACN,UAAU,EAAE;QACZ,WAAW,EAAE;QACb,eAAe,EAAE;QACjB,WAAW;QAEX,UAAU;QACV,SAAS,CAAC;YACR,IAAI;gBAAE;YAAK,GAAG,OAAO;QACvB;QAEA,aAAa,CAAC;YACZ,IAAI;gBAAE;YAAS,GAAG,OAAO;QAC3B;QAEA,cAAc,CAAC;YACb,IAAI;gBAAE;YAAU,GAAG,OAAO;QAC5B;QAEA,kBAAkB,CAAC;YACjB,IAAI;gBAAE;YAAc,GAAG,OAAO;QAChC;QAEA,cAAc,CAAC;YACb,IAAI;gBAAE;YAAU,GAAG,OAAO;QAC5B;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,UAAU,MAAM,QAAQ;YACxB,eAAe,MAAM,aAAa;QACpC,CAAC;AACH,IAEF;IACE,MAAM;AACR;AAKG,MAAM,iBAAiB;;IAAM,OAAA;kCAAQ,CAAC,QAAU,MAAM,IAAI;;AAAA;GAApD;;QAAuB;;;AAC7B,MAAM,cAAc;;IAAM,OAAA;+BAAQ,CAAC,QAAU,MAAM,QAAQ;;AAAA;IAArD;;QAAoB;;;AAC1B,MAAM,eAAe;;IAAM,OAAA;gCAAQ,CAAC,QAAU,MAAM,SAAS;;AAAA;IAAvD;;QAAqB;;;AAC3B,MAAM,mBAAmB;;IAAM,OAAA;oCAAQ,CAAC,QAAU,MAAM,aAAa;;AAAA;IAA/D;;QAAyB;;;AAC/B,MAAM,eAAe;;IAAM,OAAA;gCAAQ,CAAC,QAAU,MAAM,SAAS;;AAAA;IAAvD;;QAAqB;;;AAG3B,MAAM,iBAAiB;;IAAM,OAAA;kCAAQ,CAAC,QAAU,CAAC;gBACtD,SAAS,MAAM,OAAO;gBACtB,aAAa,MAAM,WAAW;gBAC9B,cAAc,MAAM,YAAY;gBAChC,kBAAkB,MAAM,gBAAgB;gBACxC,cAAc,MAAM,YAAY;YAClC,CAAC;;AAAC;IANW;;QAAuB;;;AAS7B,MAAM,UAAU;;IACrB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG;IAE1B,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,2DAA2D;YAC3D,QAAQ,GAAG,CAAC,eAAe;YAE3B,yBAAyB;YACzB,QAAQ,oHAAA,CAAA,WAAQ,CAAC,IAAI;YACrB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,4DAA4D;YAC5D,QAAQ;YACR,+BAA+B;YAC/B,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAgB;QAClD;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,wDAAwD;YACxD,0CAA0C;YAC1C,QAAQ,GAAG,CAAC;YAEZ,yBAAyB;YACzB,QAAQ,oHAAA,CAAA,WAAQ,CAAC,IAAI;YACrB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAyB;QAC3D;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA,iBAAiB,CAAC,CAAC;QACnB,WAAW,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;QAC1B,WAAW,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;QAC1B,OAAO,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;QACtB,SAAS,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;IAC1B;AACF;IAvDa;;QACe;;;AAyDrB,MAAM,iBAAiB;;IAC5B,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,gBAAgB,EAAE,YAAY,EAAE,GAAG;IACtE,MAAM,EAAE,IAAI,EAAE,GAAG;IAEjB,MAAM,eAAe;QACnB,IAAI;YACF,wDAAwD;YACxD,mDAAmD;YACnD,QAAQ,GAAG,CAAC;YAEZ,yBAAyB;YACzB,YAAY,oHAAA,CAAA,WAAQ,CAAC,QAAQ;YAC7B,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA0B;QAC5D;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,wDAAwD;YACxD,8DAA8D;YAC9D,QAAQ,GAAG,CAAC,kCAAkC;YAE9C,2BAA2B;YAC3B,aAAa,EAAE;YACf,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA2B;QAC7D;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,wDAAwD;YACxD,6DAA6D;YAC7D,QAAQ,GAAG,CAAC;YAEZ,2BAA2B;YAC3B,iBAAiB,EAAE;YACnB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA+B;QACjE;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,QAAS,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,KAAK,SAAU;YAC3D,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;QAEA,IAAI;YACF,wDAAwD;YACxD,sDAAsD;YACtD,QAAQ,GAAG,CAAC;YAEZ,mCAAmC;YACnC,MAAM,gBAA2B;gBAC/B,YAAY;gBACZ,eAAe;gBACf,eAAe;gBACf,eAAe;gBACf,gBAAgB;gBAChB,YAAY;gBACZ,kBAAkB;gBAClB,qBAAqB;gBACrB,iBAAiB;oBACf;wBAAE,WAAW;wBAAK,aAAa;wBAAqB,WAAW;oBAAI;oBACnE;wBAAE,WAAW;wBAAK,aAAa;wBAAe,WAAW;oBAAI;iBAC9D;gBACD,YAAY;oBACV;wBAAE,MAAM;wBAAc,OAAO;oBAAI;oBACjC;wBAAE,MAAM;wBAAc,OAAO;oBAAI;oBACjC;wBAAE,MAAM;wBAAc,OAAO;oBAAI;iBAClC;YACH;YAEA,aAAa;YACb,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA2B;QAC7D;IACF;IAEA,MAAM,cAAc;QAClB,MAAM,UAAU,MAAM,QAAQ,UAAU,CAAC;YACvC;YACA;eACI,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,SAAS,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,UAAU;gBAAC;aAAgB,GAAG,EAAE;SAC5E;QAED,MAAM,WAAW,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,KAAK;QAC5D,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;QAEA,OAAO;YAAE,SAAS,SAAS,MAAM,KAAK;QAAE;IAC1C;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;IA9Ga;;QAC2D;QACrD;;;AA+GZ,MAAM,oBAAoB;;IAC/B,MAAM,WAAW;IACjB,MAAM,EAAE,IAAI,EAAE,GAAG;IAEjB,MAAM,iBAAiB,CAAC;QACtB,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IACjD;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,KAAK;IAC1D;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,IAAI,KAAK,IAAI,KAAK,WAAW;YAC3B,yCAAyC;YACzC,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,KAAK,KAAK,KAAK;QAChE,OAAO,IAAI,KAAK,IAAI,KAAK,WAAW;YAClC,mCAAmC;YACnC,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,KAAK,KAAK,EAAE;QACjE,OAAO;YACL,8CAA8C;YAC9C,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,KAAK,KAAK,KAAK;QAChE;IACF;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF;IAhCa;;QACM;QACA;;;AAiCZ,MAAM,iBAAiB;;IAC5B,MAAM,EAAE,IAAI,EAAE,GAAG;IAEjB,MAAM,mBAAmB;QACvB,OAAO,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,aAAa,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;IACpD;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI,KAAK,IAAI,KAAK,SAAS,OAAO;QAElC,MAAM,UAAU,QAAQ,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC/D,OAAO,KAAK,IAAI,KAAK,aAAa,CAAA,oBAAA,8BAAA,QAAS,SAAS,MAAK,KAAK,EAAE;IAClE;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAO,eAAe;IACxB;IAEA,MAAM,wBAAwB;QAC5B,OAAO,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,SAAS,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;IAChD;IAEA,MAAM,mBAAmB;QACvB,OAAO,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,SAAS,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;IAChD;IAEA,MAAM,iBAAiB;QACrB,OAAO,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;IACxB;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;IAvCa;;QACM", "debugId": null}}, {"offset": {"line": 894, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/app/%28dash%29/hod/page.tsx"], "sourcesContent": ["'use client';\n\n// HOD Dashboard - Overview of teachers, students, and analytics\n\nimport { useEffect, useState } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  Users,\n  BookOpen,\n  TrendingUp,\n  Bell,\n  BarChart3,\n  UserCheck,\n  GraduationCap,\n  Plus,\n  Eye,\n  AlertCircle\n} from 'lucide-react';\nimport { useAnalytics, useDataLoaders } from '@/store/useUser';\n\nexport default function HODDashboard() {\n  const { data: session } = useSession();\n  const router = useRouter();\n  const analytics = useAnalytics();\n  const { loadAnalytics } = useDataLoaders();\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    const loadData = async () => {\n      await loadAnalytics();\n      setIsLoading(false);\n    };\n    loadData();\n  }, [loadAnalytics]);\n\n  const handleCreateAnnouncement = () => {\n    router.push('/hod/announcements/create');\n  };\n\n  const handleViewTeachers = () => {\n    router.push('/hod/teachers');\n  };\n\n  const handleViewAnalytics = () => {\n    router.push('/hod/analytics');\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading dashboard...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Welcome Header */}\n      <div className=\"bg-gradient-to-r from-purple-600 to-indigo-600 rounded-lg p-6 text-white\">\n        <h1 className=\"text-2xl font-bold mb-2\">\n          Welcome, {session?.user?.name}!\n        </h1>\n        <p className=\"text-purple-100\">\n          Monitor your institution&apos;s performance and manage teachers and students effectively.\n        </p>\n      </div>\n\n      {/* Key Metrics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"p-2 bg-blue-100 rounded-lg\">\n                <Users className=\"h-5 w-5 text-blue-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Total Users</p>\n                <p className=\"text-2xl font-bold\">{analytics?.totalUsers || 150}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"p-2 bg-green-100 rounded-lg\">\n                <GraduationCap className=\"h-5 w-5 text-green-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Students</p>\n                <p className=\"text-2xl font-bold\">{analytics?.totalStudents || 120}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"p-2 bg-purple-100 rounded-lg\">\n                <UserCheck className=\"h-5 w-5 text-purple-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Teachers</p>\n                <p className=\"text-2xl font-bold\">{analytics?.totalTeachers || 25}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"p-2 bg-orange-100 rounded-lg\">\n                <BookOpen className=\"h-5 w-5 text-orange-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Subjects</p>\n                <p className=\"text-2xl font-bold\">{analytics?.totalSubjects || 15}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Activity Overview */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <TrendingUp className=\"h-5 w-5\" />\n              <span>Activity Overview</span>\n            </CardTitle>\n            <CardDescription>Current platform engagement</CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">Active Users Today</span>\n                <span className=\"font-bold\">{analytics?.activeUsersToday || 45}</span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">Active Users This Week</span>\n                <span className=\"font-bold\">{analytics?.activeUsersThisWeek || 98}</span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">Total AI Chats</span>\n                <span className=\"font-bold\">{analytics?.totalChats || 1250}</span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">Study Materials</span>\n                <span className=\"font-bold\">{analytics?.totalMaterials || 85}</span>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <BarChart3 className=\"h-5 w-5\" />\n              <span>Popular Subjects</span>\n            </CardTitle>\n            <CardDescription>Most active subjects by chat volume</CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-3\">\n              {(analytics?.popularSubjects || [\n                { subjectId: '1', subjectName: 'Japanese Language', chatCount: 450 },\n                { subjectId: '2', subjectName: 'Mathematics', chatCount: 320 },\n                { subjectId: '3', subjectName: 'English Literature', chatCount: 280 },\n              ]).map((subject, index) => (\n                <div key={subject.subjectId} className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs font-bold text-blue-600\">\n                      {index + 1}\n                    </div>\n                    <span className=\"font-medium\">{subject.subjectName}</span>\n                  </div>\n                  <Badge variant=\"secondary\">{subject.chatCount} chats</Badge>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Quick Actions */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Quick Actions</CardTitle>\n          <CardDescription>Common administrative tasks</CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n            <Button onClick={handleCreateAnnouncement} className=\"h-20 flex-col space-y-2\">\n              <Bell className=\"h-6 w-6\" />\n              <span>Create Announcement</span>\n            </Button>\n            <Button onClick={handleViewTeachers} variant=\"outline\" className=\"h-20 flex-col space-y-2\">\n              <UserCheck className=\"h-6 w-6\" />\n              <span>Manage Teachers</span>\n            </Button>\n            <Button onClick={handleViewAnalytics} variant=\"outline\" className=\"h-20 flex-col space-y-2\">\n              <BarChart3 className=\"h-6 w-6\" />\n              <span>View Analytics</span>\n            </Button>\n            <Button onClick={() => router.push('/hod/reports')} variant=\"outline\" className=\"h-20 flex-col space-y-2\">\n              <Eye className=\"h-6 w-6\" />\n              <span>Generate Reports</span>\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Recent Announcements */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-2\">\n              <Bell className=\"h-5 w-5\" />\n              <span>Recent Announcements</span>\n            </div>\n            <Button onClick={handleCreateAnnouncement} size=\"sm\">\n              <Plus className=\"h-4 w-4 mr-2\" />\n              New\n            </Button>\n          </CardTitle>\n          <CardDescription>Latest announcements to teachers and students</CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-3\">\n            <div className=\"flex items-start space-x-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg\">\n              <AlertCircle className=\"h-5 w-5 text-yellow-600 mt-0.5\" />\n              <div className=\"flex-1\">\n                <p className=\"font-medium\">System Maintenance Scheduled</p>\n                <p className=\"text-sm text-gray-600\">Platform will be down for maintenance on Sunday 2-4 AM</p>\n                <p className=\"text-xs text-gray-500 mt-1\">Posted 2 hours ago</p>\n              </div>\n              <Badge variant=\"secondary\">Urgent</Badge>\n            </div>\n            \n            <div className=\"flex items-start space-x-3 p-3 bg-blue-50 border border-blue-200 rounded-lg\">\n              <Bell className=\"h-5 w-5 text-blue-600 mt-0.5\" />\n              <div className=\"flex-1\">\n                <p className=\"font-medium\">New AI Features Available</p>\n                <p className=\"text-sm text-gray-600\">Enhanced voice recognition and grammar analysis now live</p>\n                <p className=\"text-xs text-gray-500 mt-1\">Posted 1 day ago</p>\n              </div>\n              <Badge variant=\"outline\">Info</Badge>\n            </div>\n            \n            <div className=\"flex items-start space-x-3 p-3 bg-green-50 border border-green-200 rounded-lg\">\n              <GraduationCap className=\"h-5 w-5 text-green-600 mt-0.5\" />\n              <div className=\"flex-1\">\n                <p className=\"font-medium\">Teacher Training Workshop</p>\n                <p className=\"text-sm text-gray-600\">AI Avatar customization workshop scheduled for next Friday</p>\n                <p className=\"text-xs text-gray-500 mt-1\">Posted 3 days ago</p>\n              </div>\n              <Badge variant=\"outline\">Event</Badge>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Teacher Performance */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Teacher Performance</CardTitle>\n          <CardDescription>Top performing teachers this month</CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-3\">\n            {[\n              { name: 'Tanaka Sensei', subject: 'Japanese Language', students: 45, rating: 4.9 },\n              { name: 'Smith Sensei', subject: 'Mathematics', students: 38, rating: 4.8 },\n              { name: 'Johnson Sensei', subject: 'English Literature', students: 42, rating: 4.7 },\n            ].map((teacher) => (\n              <div key={teacher.name} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold\">\n                    {teacher.name.charAt(0)}\n                  </div>\n                  <div>\n                    <p className=\"font-medium\">{teacher.name}</p>\n                    <p className=\"text-sm text-gray-600\">{teacher.subject}</p>\n                  </div>\n                </div>\n                <div className=\"text-right\">\n                  <p className=\"font-medium\">{teacher.students} students</p>\n                  <p className=\"text-sm text-gray-600\">★ {teacher.rating}</p>\n                </div>\n              </div>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,gEAAgE;AAEhE;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;AAtBA;;;;;;;;;AAwBe,SAAS;QA2CJ;;IA1ClB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,YAAY,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IAC7B,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;mDAAW;oBACf,MAAM;oBACN,aAAa;gBACf;;YACA;QACF;iCAAG;QAAC;KAAc;IAElB,MAAM,2BAA2B;QAC/B,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,qBAAqB;QACzB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,sBAAsB;QAC1B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAA0B;4BAC5B,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,IAAI;4BAAC;;;;;;;kCAEhC,6LAAC;wBAAE,WAAU;kCAAkB;;;;;;;;;;;;0BAMjC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;0DAAsB,CAAA,sBAAA,gCAAA,UAAW,UAAU,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMpE,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;kDAE3B,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;0DAAsB,CAAA,sBAAA,gCAAA,UAAW,aAAa,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMvE,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;0DAAsB,CAAA,sBAAA,gCAAA,UAAW,aAAa,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMvE,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;0DAAsB,CAAA,sBAAA,gCAAA,UAAW,aAAa,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6LAAC;oDAAK,WAAU;8DAAa,CAAA,sBAAA,gCAAA,UAAW,gBAAgB,KAAI;;;;;;;;;;;;sDAE9D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6LAAC;oDAAK,WAAU;8DAAa,CAAA,sBAAA,gCAAA,UAAW,mBAAmB,KAAI;;;;;;;;;;;;sDAEjE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6LAAC;oDAAK,WAAU;8DAAa,CAAA,sBAAA,gCAAA,UAAW,UAAU,KAAI;;;;;;;;;;;;sDAExD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6LAAC;oDAAK,WAAU;8DAAa,CAAA,sBAAA,gCAAA,UAAW,cAAc,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMlE,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,qNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;8CACZ,CAAC,CAAA,sBAAA,gCAAA,UAAW,eAAe,KAAI;wCAC9B;4CAAE,WAAW;4CAAK,aAAa;4CAAqB,WAAW;wCAAI;wCACnE;4CAAE,WAAW;4CAAK,aAAa;4CAAe,WAAW;wCAAI;wCAC7D;4CAAE,WAAW;4CAAK,aAAa;4CAAsB,WAAW;wCAAI;qCACrE,EAAE,GAAG,CAAC,CAAC,SAAS,sBACf,6LAAC;4CAA4B,WAAU;;8DACrC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,QAAQ;;;;;;sEAEX,6LAAC;4DAAK,WAAU;sEAAe,QAAQ,WAAW;;;;;;;;;;;;8DAEpD,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;;wDAAa,QAAQ,SAAS;wDAAC;;;;;;;;2CAPtC,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgBrC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAEnB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;oCAA0B,WAAU;;sDACnD,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAoB,SAAQ;oCAAU,WAAU;;sDAC/D,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAqB,SAAQ;oCAAU,WAAU;;sDAChE,6LAAC,qNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS,IAAM,OAAO,IAAI,CAAC;oCAAiB,SAAQ;oCAAU,WAAU;;sDAC9E,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOd,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAS;wCAA0B,MAAK;;0DAC9C,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAIrC,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAEnB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAE5C,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAY;;;;;;;;;;;;8CAG7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAE5C,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;8CAG3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAE5C,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOjC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAEnB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,MAAM;oCAAiB,SAAS;oCAAqB,UAAU;oCAAI,QAAQ;gCAAI;gCACjF;oCAAE,MAAM;oCAAgB,SAAS;oCAAe,UAAU;oCAAI,QAAQ;gCAAI;gCAC1E;oCAAE,MAAM;oCAAkB,SAAS;oCAAsB,UAAU;oCAAI,QAAQ;gCAAI;6BACpF,CAAC,GAAG,CAAC,CAAC,wBACL,6LAAC;oCAAuB,WAAU;;sDAChC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,QAAQ,IAAI,CAAC,MAAM,CAAC;;;;;;8DAEvB,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAe,QAAQ,IAAI;;;;;;sEACxC,6LAAC;4DAAE,WAAU;sEAAyB,QAAQ,OAAO;;;;;;;;;;;;;;;;;;sDAGzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;;wDAAe,QAAQ,QAAQ;wDAAC;;;;;;;8DAC7C,6LAAC;oDAAE,WAAU;;wDAAwB;wDAAG,QAAQ,MAAM;;;;;;;;;;;;;;mCAZhD,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBpC;GA3RwB;;QACI,iJAAA,CAAA,aAAU;QACrB,qIAAA,CAAA,YAAS;QACN,0HAAA,CAAA,eAAY;QACJ,0HAAA,CAAA,iBAAc;;;KAJlB", "debugId": null}}]}